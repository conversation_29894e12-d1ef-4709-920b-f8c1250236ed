# 需求 [add_cache_for_search]

## 反馈

1. Fred反馈App 首页各城市新上市的房源信息，添加缓存，后台异步更新缓存

## 需求提出人:    Fred

## 修改人：       Lu<PERSON> xiaowei

## 提出日期:      2025-08-30

## 解决办法

1. 方案概述
本方案实现一个通用的、按需生成的动态查询缓存系统。缓存的生命周期由接口请求触发和控制，而非预加载。系统会为每个独立的查询参数组合维护一个独立的缓存条目，并记
录详细的元数据和统计信息，以便于监控和性能分析。
2. 核心组件定义(`src/libapp/propCache.coffee`)
  * 2.1. 缓存存储变量
      * 名称: QUERY_CACHE
      * 结构: Object，作为一个 Map 使用。
          * 键 (Key): String。由请求参数 body 哈希生成的唯一字符串 cacheKey。
          * 值 (Value): Object。包含数据和元信息的缓存条目对象。
      * 作用: 作为主缓存容器，在内存中存储所有缓存的查询结果。
  * 2.1.1. 内存管理机制
      * 最大缓存条目数限制:
          * 名称: MAX_CACHE_ENTRIES
          * 建议值: 1000
          * 作用: 防止缓存条目无限增长导致内存溢出。
      * LRU淘汰阈值:
          * 名称: LRU_CLEANUP_THRESHOLD
          * 建议值: 800 (MAX_CACHE_ENTRIES的80%)
          * 作用: 当缓存条目数达到此阈值时，触发LRU淘汰机制。
      * LRU淘汰策略:
          * 当缓存条目数超过LRU_CLEANUP_THRESHOLD时，按照最近访问时间淘汰最旧的条目
          * 每次淘汰 (MAX_CACHE_ENTRIES - LRU_CLEANUP_THRESHOLD) / 2 个条目
          * 淘汰时优先清理已过期的条目，然后按lastAccessTime排序淘汰最旧的条目
  * 2.2. 缓存条目结构 (Cache Entry Structure)
      * 每个保存在 QUERY_CACHE 中的值都是一个包含以下字段的对象：
          * props: Array - 数据库实时查询返回的结果。
          * ts: Number - 缓存创建时的时间戳 (e.g., Date.now())。
          * expTs: Number - 缓存的绝对过期时间戳 (计算方式: ts + 缓存有效时长)。
          * lastAccessTime: Number - 最后一次访问时间戳，用于LRU淘汰策略。
          * queryDuration: Number - 生成该缓存数据时，数据库查询所消耗的时长（毫秒）。
          * totalDuration： Number - 该缓存条目从创建到最后一次更新所消耗的总时长（毫秒）。
          * hitCount: Number - 该缓存条目被命中的总次数。
          * updateCount: Number - 该缓存条目被更新（重新查询）的总次数(初始值为1)。
          * body: Object - API请求的body参数
  * 2.3. 缓存配置常量
      * 名称: CACHE_DEFAULT_EXPIRATION_MS
      * 建议值: 5 * 60 * 1000 (5分钟)
      * 作用: 定义缓存的默认有效时长。

      * 名称: CACHE_STATS_INTERVAL_MS
      * 建议值: 60 * 60 * 1000 (一小时)
      * 作用: 定义后台打印缓存统计信息报告的周期。

      * 名称: MAX_CACHE_ENTRIES
      * 建议值: 1000
      * 作用: 定义缓存容器中允许的最大条目数量，防止内存无限增长。

      * 名称: LRU_CLEANUP_THRESHOLD
      * 建议值: 800
      * 作用: 定义触发LRU淘汰机制的阈值，当缓存条目数达到此值时开始淘汰。

      * 名称: LRU_CLEANUP_BATCH_SIZE
      * 建议值: 100
      * 作用: 定义每次LRU淘汰时清理的条目数量。
3. 缓存键 (Cache Key) 生成策略
为确保每个不同的查询参数组合都有唯一的缓存键，我们将对请求体 req.body (不含 useCache 字段) 进行哈希处理。
  * 流程:
      1. 规范化 (Canonicalization): 创建一个新的、不含 useCache 字段的 body
        副本。对该副本对象的键（keys）进行字母顺序排序，以确保内容相同但键顺序不同的对象能生成一致的输出。
      2. 字符串化 (Stringification): 将上一步排序后的对象转换为一个紧凑的JSON字符串。
      3. 哈希计算 (Hashing): `src/lib/helpers_string.coffee`下的`getHash`函数。
4. 接口缓存处理流程
在 search 接口(`src/apps/80_sites/AppRM/prop/resources.coffee`)的逻辑中，将按以下流程处理请求：
  1. 检查缓存控制参数:
      * 判断 req.body.useCache 是否为 true。
      * 若为 `false` 或不存在: 直接进入 步骤6 (执行数据库实时查询)，查询结果直接返回，不进行任何缓存操作。
  2. 生成缓存键:
      * 如果 useCache 为 true，则按照 第3节 描述的策略，根据 req.body 生成 cacheKey。
  3. 查找缓存:
      * 使用 cacheKey 在 QUERY_CACHE 中查找对应的缓存条目 cachedEntry。
  4. 处理缓存命中 (Cache Hit):
      * 条件: cachedEntry 存在，并且当前时间 Date.now() 小于 cachedEntry.expTs (缓存未过期)。
      * 操作:
        a.  增加缓存命中次数: cachedEntry.hitCount++。
        b.  更新最后访问时间: cachedEntry.lastAccessTime = Date.now()。
        c.  直接返回 cachedEntry.props 给客户端。
        d.  请求处理结束。
  5. 处理缓存未命中或过期 (Cache Miss / Expired):
      * 条件: cachedEntry 不存在，或 Date.now() 大于等于 cachedEntry.expTs。
      * 操作: 进入 步骤6 (执行数据库实时查询)，并告知后续步骤需要更新缓存。
  6. 执行数据库实时查询:
      * 记录查询开始时间 startTime。
      * 执行原有的数据库查询逻辑。
      * 记录查询结束时间 endTime，计算查询耗时 duration。
      * 如果请求来自步骤1 (useCache: false): 直接返回查询结果。
      * 如果请求来自步骤5 (需要更新缓存): 进入 步骤7 (更新缓存)。
  7. 更新缓存:
      * 检查内存使用情况并执行LRU淘汰 (如果需要):
          * 如果当前缓存条目数 >= LRU_CLEANUP_THRESHOLD，执行LRU淘汰:
            a. 收集所有已过期的缓存条目并删除。
            b. 如果删除过期条目后仍超过阈值，按 lastAccessTime 排序，删除最旧的条目。
            c. 每次淘汰 LRU_CLEANUP_BATCH_SIZE 个条目。
      * 获取旧的缓存条目 oldEntry (如果存在的话)。
      * 创建一个新的缓存条目 newEntry：
          * props: 第6步中查询到的新结果。
          * ts: Date.now()。
          * expTs: Date.now() + CACHE_DEFAULT_EXPIRATION_MS。
          * lastAccessTime: Date.now()。
          * queryDuration: duration。
          * totalDuration: 如果 oldEntry 存在,oldEntry.totalDuration + duration，否则为 duration。
          * hitCount: 如果 oldEntry 存在，则继承 oldEntry.hitCount，否则为 0。
          * updateCount: 如果 oldEntry 存在，则为 oldEntry.updateCount + 1，否则为 1。
          * body: API请求参数
      * 将新的缓存条目保存到缓存中: QUERY_CACHE[cacheKey] = newEntry。
      * 将 newEntry.props 返回给客户端。
5. 统计与监控
  * 5.1. 统计报告函数
      * 函数名: logCacheReport
      * 逻辑:
          1. 遍历 QUERY_CACHE 中的所有条目。
          2. 聚合计算统计信息，例如：
              * 缓存条目总数。
              * 所有条目的 hitCount 总和。
              * 所有条目的 updateCount 总和。
              * 总内存占用的近似值（通过条目数估算）。
              * 过期条目数量。
              * 内存使用率 (当前条目数 / MAX_CACHE_ENTRIES)。
              * LRU淘汰统计信息（如果有的话）。
          3. 在控制台打印一份格式化的报告，展示以上信息(每条缓存的信息以及聚合结果)。
  * 5.2. 调度
      * 时机: 项目启动时。
      * 操作: 启动一个定时器 setInterval(logCacheReport, CACHE_STATS_INTERVAL_MS)，周期性地在控制台输出缓存的使用情况报告。

## 是否需要补充UT

1. 需要补充UT

## 确认日期:    2025-09-09

## online-step

1. 重启server