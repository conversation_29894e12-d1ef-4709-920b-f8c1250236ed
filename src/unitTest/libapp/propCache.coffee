should = require('should')
testHelpers = require('../00_common/helpers')
propCache = require('../../built/libapp/propCache')

# ./unitTest/test.sh -f libapp/propCache.js
describe 'PropCache function tests', ->
  before (done) ->
    @timeout(300000)
    done()

  describe 'generateCacheKey', ->
    tests = [
      {
        description: 'should generate cache key for simple object'
        input: { city: 'Toronto', price: 500000 }
        shouldBeConsistent: true
      }
      {
        description: 'should ignore useCache field'
        input: { city: 'Toronto', price: 500000, useCache: true }
        compareWith: { city: 'Toronto', price: 500000 }
        shouldBeEqual: true
      }
      {
        description: 'should generate same key for different key order'
        input: { price: 500000, city: 'Toronto' }
        compareWith: { city: 'Toronto', price: 500000 }
        shouldBeEqual: true
      }
      {
        description: 'should generate different keys for different values'
        input: { city: 'Toronto', price: 500000 }
        compareWith: { city: 'Toronto', price: 600000 }
        shouldBeEqual: false
      }
      {
        description: 'should handle empty object'
        input: {}
        shouldBeConsistent: true
      }
      {
        description: 'should handle complex nested object'
        input: {
          city: 'Toronto'
          price: { min: 500000, max: 1000000 }
          features: ['garage', 'pool']
        }
        shouldBeConsistent: true
      }
    ]

    tests.forEach (test) ->
      it test.description, (done) ->
        key1 = propCache.generateCacheKey(test.input)
        
        # 测试一致性
        if test.shouldBeConsistent
          key2 = propCache.generateCacheKey(test.input)
          should.equal key1, key2
        
        # 测试与另一个对象的比较
        if test.compareWith?
          key2 = propCache.generateCacheKey(test.compareWith)
          if test.shouldBeEqual
            should.equal key1, key2
          else
            should.notEqual key1, key2
        
        # 确保生成的键是字符串
        should(key1).be.a.String()
        should(key1.length).be.greaterThan(0)
        
        done()

  describe 'getCacheEntry', ->
    it 'should return null for non-existent cache key', (done) ->
      result = propCache.getCacheEntry('non-existent-key')
      should.equal result, null
      done()

    it 'should return cache entry for existing key', (done) ->
      # 首先设置一个缓存条目
      testKey = 'test-key-1'
      testProps = [{ id: 1, address: 'Test Address' }]
      testCnt = 1
      testQueryDuration = 100
      testBody = { city: 'Toronto' }

      propCache.setCacheEntry({
        cacheKey: testKey
        props: testProps
        cnt: testCnt
        queryDuration: testQueryDuration
        body: testBody
      })

      # 获取缓存条目
      result = propCache.getCacheEntry(testKey)
      
      should(result).not.be.null()
      should.deepEqual result.props, testProps
      should.equal result.cnt, testCnt
      should.equal result.queryDuration, testQueryDuration
      should.deepEqual result.body, testBody
      should(result.ts).be.a.Number()
      should(result.expTs).be.a.Number()
      should(result.lastAccessTime).be.a.Number()
      should.equal result.hitCount, 1  # 应该增加命中次数
      should.equal result.updateCount, 1
      
      done()

    it 'should update hitCount and lastAccessTime on access', (done) ->
      # 设置缓存条目
      testKey = 'test-key-2'
      propCache.setCacheEntry({
        cacheKey: testKey
        props: []
        cnt: 0
        queryDuration: 50
        body: {}
      })

      # 第一次访问
      result1 = propCache.getCacheEntry(testKey)
      firstAccessTime = result1.lastAccessTime
      should.equal result1.hitCount, 1

      # 等待一小段时间后再次访问
      setTimeout ->
        result2 = propCache.getCacheEntry(testKey)
        should.equal result2.hitCount, 2
        should(result2.lastAccessTime).be.greaterThan(firstAccessTime)
        done()
      , 10

    it 'should return null for expired cache entry', (done) ->
      # 这个测试比较难实现，因为默认过期时间是5分钟
      # 我们可以测试逻辑，但不能轻易测试实际过期
      # 这里我们至少验证过期检查的存在
      testKey = 'test-key-3'
      propCache.setCacheEntry({
        cacheKey: testKey
        props: []
        cnt: 0
        queryDuration: 50
        body: {}
      })

      result = propCache.getCacheEntry(testKey)
      should(result).not.be.null()
      should(result.expTs).be.greaterThan(Date.now())
      
      done()

  describe 'setCacheEntry', ->
    it 'should set new cache entry with correct properties', (done) ->
      testKey = 'set-test-key-1'
      testProps = [{ id: 1, address: 'Test Address' }]
      testCnt = 5
      testQueryDuration = 200
      testBody = { city: 'Vancouver', price: 800000 }

      propCache.setCacheEntry({
        cacheKey: testKey
        props: testProps
        cnt: testCnt
        queryDuration: testQueryDuration
        body: testBody
      })

      # 验证设置的缓存条目
      result = propCache.getCacheEntry(testKey)
      should(result).not.be.null()
      should.deepEqual result.props, testProps
      should.equal result.cnt, testCnt
      should.equal result.queryDuration, testQueryDuration
      should.equal result.totalDuration, testQueryDuration  # 新条目，总时长等于查询时长
      should.deepEqual result.body, testBody
      should.equal result.hitCount, 1  # getCacheEntry 会增加命中次数
      should.equal result.updateCount, 1
      should(result.ts).be.a.Number()
      should(result.expTs).be.a.Number()
      should(result.lastAccessTime).be.a.Number()

      done()

    it 'should update existing cache entry correctly', (done) ->
      testKey = 'set-test-key-2'
      
      # 第一次设置
      propCache.setCacheEntry({
        cacheKey: testKey
        props: [{ id: 1 }]
        cnt: 1
        queryDuration: 100
        body: { city: 'Toronto' }
      })

      # 获取旧条目
      oldEntry = propCache.getCacheEntry(testKey)
      
      # 第二次设置（更新）
      newProps = [{ id: 1 }, { id: 2 }]
      newCnt = 2
      newQueryDuration = 150
      
      propCache.setCacheEntry({
        cacheKey: testKey
        props: newProps
        cnt: newCnt
        queryDuration: newQueryDuration
        body: { city: 'Toronto' }
        oldEntry: oldEntry
      })

      # 验证更新后的缓存条目
      result = propCache.getCacheEntry(testKey)
      should(result).not.be.null()
      should.deepEqual result.props, newProps
      should.equal result.cnt, newCnt
      should.equal result.queryDuration, newQueryDuration
      should.equal result.totalDuration, 100 + newQueryDuration  # 累计总时长
      should.equal result.hitCount, oldEntry.hitCount + 1  # getCacheEntry 增加了命中次数
      should.equal result.updateCount, 2  # 更新次数应该增加

      done()

    it 'should handle cache entry without oldEntry parameter', (done) ->
      testKey = 'set-test-key-3'
      
      propCache.setCacheEntry({
        cacheKey: testKey
        props: []
        cnt: 0
        queryDuration: 75
        body: {}
        # 没有提供 oldEntry
      })

      result = propCache.getCacheEntry(testKey)
      should(result).not.be.null()
      should.equal result.totalDuration, 75
      should.equal result.hitCount, 1  # getCacheEntry 增加的
      should.equal result.updateCount, 1

      done()
