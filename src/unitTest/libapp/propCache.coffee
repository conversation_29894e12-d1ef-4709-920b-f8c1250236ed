should = require('should')
testHelpers = require('../00_common/helpers')
propCache = require('../../built/libapp/propCache')

# ./unitTest/test.sh -f libapp/propCache.js
describe 'PropCache function tests', ->
  before (done) ->
    @timeout(300000)
    done()

  describe 'generateCacheKey', ->
    tests = [
      {
        description: 'should generate cache key for simple object'
        input: { city: 'Toronto', price: 500000 }
        shouldBeConsistent: true
      }
      {
        description: 'should ignore useCache field'
        input: { city: 'Toronto', price: 500000, useCache: true }
        compareWith: { city: 'Toronto', price: 500000 }
        shouldBeEqual: true
      }
      {
        description: 'should generate same key for different key order'
        input: { price: 500000, city: 'Toronto' }
        compareWith: { city: 'Toronto', price: 500000 }
        shouldBeEqual: true
      }
      {
        description: 'should generate different keys for different values'
        input: { city: 'Toronto', price: 500000 }
        compareWith: { city: 'Toronto', price: 600000 }
        shouldBeEqual: false
      }
      {
        description: 'should handle empty object'
        input: {}
        shouldBeConsistent: true
      }
      {
        description: 'should handle complex nested object'
        input: {
          city: 'Toronto'
          price: { min: 500000, max: 1000000 }
          features: ['garage', 'pool']
        }
        shouldBeConsistent: true
      }
    ]

    tests.forEach (test) ->
      it test.description, (done) ->
        key1 = propCache.generateCacheKey(test.input)
        
        # 测试一致性
        if test.shouldBeConsistent
          key2 = propCache.generateCacheKey(test.input)
          should.equal key1, key2
        
        # 测试与另一个对象的比较
        if test.compareWith?
          key2 = propCache.generateCacheKey(test.compareWith)
          if test.shouldBeEqual
            should.equal key1, key2
          else
            should.notEqual key1, key2
        
        # 确保生成的键是字符串
        should(key1).be.a.String()
        should(key1.length).be.greaterThan(0)
        
        done()

  describe 'getCacheEntry', ->
    it 'should return null for non-existent cache key', (done) ->
      result = propCache.getCacheEntry('non-existent-key')
      should.equal result, null
      done()

    it 'should return cache entry for existing key', (done) ->
      # 首先设置一个缓存条目
      testKey = 'test-key-1'
      testProps = [{ id: 1, address: 'Test Address' }]
      testCnt = 1
      testQueryDuration = 100
      testBody = { city: 'Toronto' }

      propCache.setCacheEntry({
        cacheKey: testKey
        props: testProps
        cnt: testCnt
        queryDuration: testQueryDuration
        body: testBody
      })

      # 获取缓存条目
      result = propCache.getCacheEntry(testKey)
      
      should(result).not.be.null()
      should.deepEqual result.props, testProps
      should.equal result.cnt, testCnt
      should.equal result.queryDuration, testQueryDuration
      should.deepEqual result.body, testBody
      should(result.ts).be.a.Number()
      should(result.expTs).be.a.Number()
      should(result.lastAccessTime).be.a.Number()
      should.equal result.hitCount, 1  # 应该增加命中次数
      should.equal result.updateCount, 1
      
      done()

    it 'should update hitCount and lastAccessTime on access', (done) ->
      # 设置缓存条目
      testKey = 'test-key-2'
      propCache.setCacheEntry({
        cacheKey: testKey
        props: []
        cnt: 0
        queryDuration: 50
        body: {}
      })

      # 第一次访问
      result1 = propCache.getCacheEntry(testKey)
      firstAccessTime = result1.lastAccessTime
      should.equal result1.hitCount, 1

      # 等待一小段时间后再次访问
      setTimeout ->
        result2 = propCache.getCacheEntry(testKey)
        should.equal result2.hitCount, 2
        should(result2.lastAccessTime).be.greaterThan(firstAccessTime)
        done()
      , 10

    it 'should return null for expired cache entry', (done) ->
      # 这个测试比较难实现，因为默认过期时间是5分钟
      # 我们可以测试逻辑，但不能轻易测试实际过期
      # 这里我们至少验证过期检查的存在
      testKey = 'test-key-3'
      propCache.setCacheEntry({
        cacheKey: testKey
        props: []
        cnt: 0
        queryDuration: 50
        body: {}
      })

      result = propCache.getCacheEntry(testKey)
      should(result).not.be.null()
      should(result.expTs).be.greaterThan(Date.now())
      
      done()

  describe 'setCacheEntry', ->
    it 'should set new cache entry with correct properties', (done) ->
      testKey = 'set-test-key-1'
      testProps = [{ id: 1, address: 'Test Address' }]
      testCnt = 5
      testQueryDuration = 200
      testBody = { city: 'Vancouver', price: 800000 }

      propCache.setCacheEntry({
        cacheKey: testKey
        props: testProps
        cnt: testCnt
        queryDuration: testQueryDuration
        body: testBody
      })

      # 验证设置的缓存条目
      result = propCache.getCacheEntry(testKey)
      should(result).not.be.null()
      should.deepEqual result.props, testProps
      should.equal result.cnt, testCnt
      should.equal result.queryDuration, testQueryDuration
      should.equal result.totalDuration, testQueryDuration  # 新条目，总时长等于查询时长
      should.deepEqual result.body, testBody
      should.equal result.hitCount, 1  # getCacheEntry 会增加命中次数
      should.equal result.updateCount, 1
      should(result.ts).be.a.Number()
      should(result.expTs).be.a.Number()
      should(result.lastAccessTime).be.a.Number()

      done()

    it 'should update existing cache entry correctly', (done) ->
      testKey = 'set-test-key-2'
      
      # 第一次设置
      propCache.setCacheEntry({
        cacheKey: testKey
        props: [{ id: 1 }]
        cnt: 1
        queryDuration: 100
        body: { city: 'Toronto' }
      })

      # 获取旧条目
      oldEntry = propCache.getCacheEntry(testKey)
      
      # 第二次设置（更新）
      newProps = [{ id: 1 }, { id: 2 }]
      newCnt = 2
      newQueryDuration = 150
      
      propCache.setCacheEntry({
        cacheKey: testKey
        props: newProps
        cnt: newCnt
        queryDuration: newQueryDuration
        body: { city: 'Toronto' }
        oldEntry: oldEntry
      })

      # 验证更新后的缓存条目
      result = propCache.getCacheEntry(testKey)
      should(result).not.be.null()
      should.deepEqual result.props, newProps
      should.equal result.cnt, newCnt
      should.equal result.queryDuration, newQueryDuration
      should.equal result.totalDuration, 100 + newQueryDuration  # 累计总时长
      should.equal result.hitCount, oldEntry.hitCount + 1  # getCacheEntry 增加了命中次数
      should.equal result.updateCount, 2  # 更新次数应该增加

      done()

    it 'should handle cache entry without oldEntry parameter', (done) ->
      testKey = 'set-test-key-3'
      
      propCache.setCacheEntry({
        cacheKey: testKey
        props: []
        cnt: 0
        queryDuration: 75
        body: {}
        # 没有提供 oldEntry
      })

      result = propCache.getCacheEntry(testKey)
      should(result).not.be.null()
      should.equal result.totalDuration, 75
      should.equal result.hitCount, 1  # getCacheEntry 增加的
      should.equal result.updateCount, 1

      done()

  describe 'cleanupExpiredEntries', ->
    beforeEach (done) ->
      propCache.clearAllCache()
      propCache.resetLruStats()
      done()

    it 'should return 0 when no expired entries exist', (done) ->
      # 设置一些未过期的缓存条目
      propCache.setCacheEntry({
        cacheKey: 'key1'
        props: []
        cnt: 0
        queryDuration: 50
        body: {}
      })

      result = propCache.cleanupExpiredEntries()
      should.equal result, 0
      should.equal propCache.getCacheCount(), 1
      done()

    it 'should clean up expired entries', (done) ->
      # 这个测试比较难实现，因为我们无法轻易创建过期的条目
      # 但我们可以测试函数的存在和基本功能
      result = propCache.cleanupExpiredEntries()
      should(result).be.a.Number()
      should(result).be.greaterThanOrEqual(0)
      done()

  describe 'performLRUCleanup', ->
    beforeEach (done) ->
      propCache.clearAllCache()
      propCache.resetLruStats()
      done()

    it 'should return 0 when no entries exist', (done) ->
      result = propCache.performLRUCleanup(5)
      should.equal result, 0
      done()

    it 'should clean up specified number of entries', (done) ->
      # 创建多个缓存条目
      for i in [1..5]
        propCache.setCacheEntry({
          cacheKey: "key#{i}"
          props: []
          cnt: 0
          queryDuration: 50
          body: { id: i }
        })
        # 稍微延迟以确保不同的访问时间
        if i < 5
          setTimeout(->
          , 1)

      should.equal propCache.getCacheCount(), 5

      # 清理2个条目
      result = propCache.performLRUCleanup(2)
      should.equal result, 2
      should.equal propCache.getCacheCount(), 3

      # 验证LRU统计
      stats = propCache.getLruStats()
      should.equal stats.lruCleaned, 2

      done()

    it 'should not clean more entries than exist', (done) ->
      # 创建2个缓存条目
      propCache.setCacheEntry({
        cacheKey: 'key1'
        props: []
        cnt: 0
        queryDuration: 50
        body: {}
      })
      propCache.setCacheEntry({
        cacheKey: 'key2'
        props: []
        cnt: 0
        queryDuration: 50
        body: {}
      })

      should.equal propCache.getCacheCount(), 2

      # 尝试清理5个条目，但只有2个存在
      result = propCache.performLRUCleanup(5)
      should.equal result, 2
      should.equal propCache.getCacheCount(), 0

      done()

  describe 'performMemoryManagement', ->
    beforeEach (done) ->
      propCache.clearAllCache()
      propCache.resetLruStats()
      done()

    it 'should not trigger cleanup when below threshold', (done) ->
      # 创建少量缓存条目（低于LRU_CLEANUP_THRESHOLD）
      for i in [1..10]
        propCache.setCacheEntry({
          cacheKey: "key#{i}"
          props: []
          cnt: 0
          queryDuration: 50
          body: { id: i }
        })

      initialStats = propCache.getLruStats()
      propCache.performMemoryManagement()

      finalStats = propCache.getLruStats()
      should.equal finalStats.totalCleanups, initialStats.totalCleanups
      done()

    it 'should exist and be callable', (done) ->
      # 基本功能测试
      should(propCache.performMemoryManagement).be.a.Function()
      propCache.performMemoryManagement()  # 应该不抛出错误
      done()

  describe 'logCacheReport', ->
    beforeEach (done) ->
      propCache.clearAllCache()
      done()

    it 'should be callable with empty cache', (done) ->
      should(propCache.logCacheReport).be.a.Function()
      propCache.logCacheReport()  # 应该不抛出错误
      done()

    it 'should be callable with cache entries', (done) ->
      # 添加一些缓存条目
      propCache.setCacheEntry({
        cacheKey: 'report-test-key'
        props: [{ id: 1 }]
        cnt: 1
        queryDuration: 100
        body: { city: 'Toronto' }
      })

      propCache.logCacheReport()  # 应该不抛出错误
      done()

  describe 'Test Helper Functions', ->
    it 'should clear all cache entries', (done) ->
      # 先清楚历史缓存
      propCache.clearAllCache()
      # 添加一些缓存条目
      propCache.setCacheEntry({
        cacheKey: 'helper-test-1'
        props: []
        cnt: 0
        queryDuration: 50
        body: {}
      })
      propCache.setCacheEntry({
        cacheKey: 'helper-test-2'
        props: []
        cnt: 0
        queryDuration: 50
        body: {}
      })

      should.equal propCache.getCacheCount(), 2

      clearedCount = propCache.clearAllCache()
      should.equal clearedCount, 2
      should.equal propCache.getCacheCount(), 0
      done()

    it 'should get current cache count', (done) ->
      propCache.clearAllCache()
      should.equal propCache.getCacheCount(), 0

      propCache.setCacheEntry({
        cacheKey: 'count-test'
        props: []
        cnt: 0
        queryDuration: 50
        body: {}
      })

      should.equal propCache.getCacheCount(), 1
      done()

    it 'should get and reset LRU stats', (done) ->
      propCache.resetLruStats()

      stats = propCache.getLruStats()
      should.equal stats.totalCleanups, 0
      should.equal stats.expiredCleaned, 0
      should.equal stats.lruCleaned, 0
      should.equal stats.lastCleanupTime, 0

      # 重置后应该保持为0
      propCache.resetLruStats()
      stats = propCache.getLruStats()
      should.equal stats.totalCleanups, 0
      done()

    it 'should get cache configuration constants', (done) ->
      config = propCache.getCacheConfig()

      should(config).be.an.Object()
      should(config.CACHE_DEFAULT_EXPIRATION_MS).be.a.Number()
      should(config.CACHE_STATS_INTERVAL_MS).be.a.Number()
      should(config.MAX_CACHE_ENTRIES).be.a.Number()
      should(config.LRU_CLEANUP_THRESHOLD).be.a.Number()
      should(config.LRU_CLEANUP_BATCH_SIZE).be.a.Number()

      # 验证一些预期的值
      should.equal config.CACHE_DEFAULT_EXPIRATION_MS, 5 * 60 * 1000  # 5分钟
      should.equal config.MAX_CACHE_ENTRIES, 1000
      should.equal config.LRU_CLEANUP_THRESHOLD, 800
      should.equal config.LRU_CLEANUP_BATCH_SIZE, 100

      done()
